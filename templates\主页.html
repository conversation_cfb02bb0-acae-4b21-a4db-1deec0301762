<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>商品数据提取工具</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/样式.css') }}" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
</head>
<body>
    <div class="container">
        <!-- 头部标题区域 -->
        <header class="header">
            <h1><i class="fas fa-shopping-cart"></i> 商品数据提取工具</h1>
            <p class="subtitle">提取商品信息并下载图片</p>
        </header>

        <!-- 配置选项区域 -->
        <section class="config-section">
            <h2><i class="fas fa-cog"></i> 配置选项</h2>
            
            <!-- API配置 -->
            <div class="config-group">
                <label for="api-url">API地址：</label>
                <input type="text" id="api-url" class="config-input" placeholder="输入API地址" />
            </div>
            
            <div class="config-group">
                <label for="cookie">Cookie：</label>
                <input type="text" id="cookie" class="config-input" placeholder="输入Cookie" />
            </div>
            
            <!-- 搜索路径配置 -->
            <div class="config-group">
                <label for="search-path">搜索路径：</label>
                <div class="input-group">
                    <input type="text" id="search-path" class="config-input" placeholder="选择搜索路径" />
                    <button type="button" class="btn btn-secondary" onclick="browseFolder('search-path')">
                        <i class="fas fa-folder-open"></i> 浏览
                    </button>
                </div>
            </div>
            
            <!-- 目标路径后缀 -->
            <div class="config-group">
                <label for="target-suffix">目标路径后缀：</label>
                <div class="input-group">
                    <input type="text" id="target-suffix" class="config-input" placeholder="输入目标路径后缀" />
                    <label class="checkbox-label">
                        <input type="checkbox" id="enable-target-suffix" checked />
                        <span class="checkmark"></span>
                        启用
                    </label>
                </div>
            </div>
            
            <!-- 忽略文件名前后字符 -->
            <div class="config-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="ignore-filename-chars" />
                    <span class="checkmark"></span>
                    忽略文件名前后字符
                </label>
                <div class="ignore-chars-config">
                    <span>忽略前</span>
                    <input type="number" id="ignore-prefix-count" class="number-input" value="20" min="0" />
                    <span>个字符</span>
                    <span>忽略后</span>
                    <input type="number" id="ignore-suffix-count" class="number-input" value="50" min="0" />
                    <span>个字符</span>
                </div>
            </div>
            
            <!-- 共享文件夹 -->
            <div class="config-group">
                <label for="shared-folder">共享文件夹：</label>
                <div class="input-group">
                    <input type="text" id="shared-folder" class="config-input" placeholder="选择共享文件夹" />
                    <button type="button" class="btn btn-secondary" onclick="browseFolder('shared-folder')">
                        <i class="fas fa-folder-open"></i> 浏览
                    </button>
                </div>
            </div>
            
            <!-- 其他选项 -->
            <div class="config-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="strict-search" checked />
                    <span class="checkmark"></span>
                    严格搜索路径限制（只搜索指定目录）
                </label>
                <p class="help-text">
                    <i class="fas fa-info-circle"></i>
                    注意：勾选"严格搜索路径限制"可确保只在指定目录中搜索
                </p>
            </div>
        </section>

        <!-- 商品数据输入区域 -->
        <section class="product-section">
            <h2><i class="fas fa-edit"></i> 商品数据输入</h2>
            <div class="input-help">
                <p><i class="fas fa-info-circle"></i> 请在下方文本框中粘贴包含商品信息的HTML数据，然后点击"提取SKU"按钮</p>
            </div>
            <textarea id="product-data" class="product-textarea" placeholder="请粘贴商品HTML数据...&#10;&#10;示例：包含 &lt;a class=&quot;pairProInfoSku productUrl&quot;&gt;SKU123&lt;/a&gt; 这样的HTML标签"></textarea>
            
            <!-- SKU搜索 -->
            <div class="sku-search-group">
                <label for="sku-search">SKU搜索：</label>
                <div class="input-group">
                    <input type="text" id="sku-search" class="config-input" placeholder="输入SKU编号" />
                    <button type="button" class="btn btn-purple" onclick="searchSkuName()">
                        <i class="fas fa-search"></i> 搜索商品名
                    </button>
                </div>
            </div>
        </section>

        <!-- 操作按钮区域 -->
        <section class="actions-section">
            <div class="button-group">
                <button type="button" class="btn btn-orange" onclick="downloadImages()">
                    <i class="fas fa-download"></i> 下载图片
                </button>
                <button type="button" class="btn btn-blue" onclick="renameFiles()">
                    <i class="fas fa-edit"></i> 重命名文件
                </button>
                <button type="button" class="btn btn-green" onclick="extractSku()">
                    <i class="fas fa-extract"></i> 提取SKU
                </button>
                <button type="button" class="btn btn-purple" onclick="searchSkuAndDownload()">
                    <i class="fas fa-search-plus"></i> SKU搜索下载
                </button>
                <button type="button" class="btn btn-red" onclick="compareSku()">
                    <i class="fas fa-balance-scale"></i> SKU对比
                </button>
                <button type="button" class="btn btn-success" onclick="downloadMissingSku()">
                    <i class="fas fa-plus-circle"></i> 下载缺失SKU
                </button>
            </div>
        </section>

        <!-- 操作日志区域 -->
        <section class="console-section">
            <h2><i class="fas fa-terminal"></i> 操作日志</h2>
            <div id="console" class="console"></div>
        </section>

        <!-- 状态栏 -->
        <footer class="status-bar">
            <span id="status">就绪</span>
        </footer>
    </div>

    <!-- 加载提示 -->
    <div id="loading" class="loading hidden">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>处理中...</span>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/主脚本.js') }}"></script>
</body>
</html>
