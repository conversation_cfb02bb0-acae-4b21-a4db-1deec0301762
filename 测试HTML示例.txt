测试用的HTML内容示例：

<div class="product-info">
    <span class="white-space no-new-line3 productHead">测试商品名称</span>
    <div class="sku-list">
        <a class="pairProInfoSku productUrl" href="#">ABC123</a>
        <a class="pairProInfoSku productUrl" href="#">DEF456</a>
        <a class="pairProInfoSku productUrl" href="#">GHI789</a>
    </div>
</div>

使用方法：
1. 复制上面的HTML内容
2. 粘贴到网页的"商品数据输入"文本框中
3. 点击"提取SKU"按钮
4. 应该能看到提取出3个SKU：ABC123, DEF456, GHI789
