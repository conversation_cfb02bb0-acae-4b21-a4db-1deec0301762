// 全局变量
let config = {};
let isLoading = false;

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadConfig();
    bindEvents();
});

// 初始化应用
function initializeApp() {
    logMessage('应用初始化完成', 'info');
    updateStatus('就绪');
}

// 加载配置
async function loadConfig() {
    try {
        const response = await fetch('/api/config');
        if (response.ok) {
            config = await response.json();
            populateConfigFields();
            logMessage('配置加载成功', 'success');
        } else {
            throw new Error('配置加载失败');
        }
    } catch (error) {
        logMessage(`配置加载错误: ${error.message}`, 'error');
    }
}

// 填充配置字段
function populateConfigFields() {
    if (config.API) {
        document.getElementById('api-url').value = config.API.url || '';
        document.getElementById('cookie').value = config.API.cookie || '';
    }
    
    if (config.SEARCH) {
        document.getElementById('search-path').value = config.SEARCH.base_path || '';
        document.getElementById('target-suffix').value = config.SEARCH.target_suffix || '';
        document.getElementById('enable-target-suffix').checked = config.SEARCH.enable_target_suffix === 'True';
        document.getElementById('ignore-filename-chars').checked = config.SEARCH.ignore_filename_chars === 'True';
        document.getElementById('ignore-prefix-count').value = config.SEARCH.ignore_prefix_count || '20';
        document.getElementById('ignore-suffix-count').value = config.SEARCH.ignore_suffix_count || '50';
    }
    
    if (config.SHARED) {
        document.getElementById('shared-folder').value = config.SHARED.folder || '';
    }
    
    if (config.OPTIONS) {
        document.getElementById('strict-search').checked = config.OPTIONS.strict_search === 'True';
    }
}

// 绑定事件
function bindEvents() {
    // 配置字段变化事件
    const configInputs = document.querySelectorAll('.config-input, input[type="checkbox"], input[type="number"]');
    configInputs.forEach(input => {
        input.addEventListener('change', saveConfigField);
    });
    
    // 回车键提交
    document.getElementById('sku-search').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchSkuName();
        }
    });
}

// 保存配置字段
async function saveConfigField(event) {
    const element = event.target;
    const fieldId = element.id;
    let section, key, value;
    
    // 根据字段ID确定配置节和键
    switch (fieldId) {
        case 'api-url':
            section = 'API';
            key = 'url';
            value = element.value;
            break;
        case 'cookie':
            section = 'API';
            key = 'cookie';
            value = element.value;
            break;
        case 'search-path':
            section = 'SEARCH';
            key = 'base_path';
            value = element.value;
            break;
        case 'target-suffix':
            section = 'SEARCH';
            key = 'target_suffix';
            value = element.value;
            break;
        case 'enable-target-suffix':
            section = 'SEARCH';
            key = 'enable_target_suffix';
            value = element.checked ? 'True' : 'False';
            break;
        case 'ignore-filename-chars':
            section = 'SEARCH';
            key = 'ignore_filename_chars';
            value = element.checked ? 'True' : 'False';
            break;
        case 'ignore-prefix-count':
            section = 'SEARCH';
            key = 'ignore_prefix_count';
            value = element.value;
            break;
        case 'ignore-suffix-count':
            section = 'SEARCH';
            key = 'ignore_suffix_count';
            value = element.value;
            break;
        case 'shared-folder':
            section = 'SHARED';
            key = 'folder';
            value = element.value;
            break;
        case 'strict-search':
            section = 'OPTIONS';
            key = 'strict_search';
            value = element.checked ? 'True' : 'False';
            break;
        default:
            return;
    }
    
    try {
        const response = await fetch('/api/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ section, key, value })
        });
        
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                logMessage(`已保存${section}.${key}配置`, 'info');
            }
        }
    } catch (error) {
        logMessage(`保存配置错误: ${error.message}`, 'error');
    }
}

// 搜索SKU商品名称
async function searchSkuName() {
    const sku = document.getElementById('sku-search').value.trim();
    if (!sku) {
        logMessage('请输入SKU编号', 'warning');
        return;
    }
    
    showLoading(true);
    updateStatus('搜索中...');
    
    try {
        const response = await fetch('/api/search_sku', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ sku })
        });
        
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                logMessage(`找到商品: ${result.product_name}`, 'success');
                logMessage(`SKU: ${result.sku}`, 'info');
                if (result.price) {
                    logMessage(`价格: ${result.price}`, 'info');
                }
            } else {
                logMessage(result.error || '搜索失败', 'error');
            }
        } else {
            throw new Error('请求失败');
        }
    } catch (error) {
        logMessage(`搜索错误: ${error.message}`, 'error');
    } finally {
        showLoading(false);
        updateStatus('就绪');
    }
}

// 提取SKU
async function extractSku() {
    const htmlContent = document.getElementById('product-data').value.trim();
    if (!htmlContent) {
        logMessage('请输入商品HTML数据', 'warning');
        return;
    }
    
    showLoading(true);
    updateStatus('提取中...');
    
    try {
        const response = await fetch('/api/extract_sku', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ html_content: htmlContent })
        });
        
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                logMessage(`找到 ${result.count} 个SKU:`, 'success');
                logMessage('─'.repeat(30), 'info');
                result.skus.forEach((sku, index) => {
                    logMessage(`${index + 1}. ${sku}`, 'info');
                });
                logMessage('─'.repeat(30), 'info');
                
                if (result.product_name) {
                    logMessage(`商品名称: ${result.product_name}`, 'info');
                }
            } else {
                logMessage(result.error || '提取失败', 'error');
            }
        } else {
            throw new Error('请求失败');
        }
    } catch (error) {
        logMessage(`提取错误: ${error.message}`, 'error');
    } finally {
        showLoading(false);
        updateStatus('就绪');
    }
}

// 下载图片
async function downloadImages() {
    const htmlContent = document.getElementById('product-data').value.trim();
    if (!htmlContent) {
        logMessage('请先输入商品HTML数据', 'warning');
        return;
    }

    showLoading(true);
    updateStatus('下载中...');

    try {
        const response = await fetch('/api/download_images', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ html_content: htmlContent })
        });

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                logMessage('图片下载完成！', 'success');
                logMessage(`下载目录: ${result.download_dir}`, 'info');
                if (result.product_name) {
                    logMessage(`商品名称: ${result.product_name}`, 'info');
                }
                logMessage('─'.repeat(50), 'info');

                result.results.forEach(item => {
                    const statusIcon = item.status === 'success' ? '✓' : '✗';
                    logMessage(`${statusIcon} ${item.message}`, item.status === 'success' ? 'success' : 'error');
                });

                const successCount = result.results.filter(r => r.status === 'success').length;
                const totalCount = result.results.length;
                logMessage(`总计: ${successCount}/${totalCount} 个SKU下载成功`, 'info');
            } else {
                logMessage(result.error || '下载失败', 'error');
            }
        } else {
            throw new Error('下载请求失败');
        }
    } catch (error) {
        logMessage(`下载错误: ${error.message}`, 'error');
    } finally {
        showLoading(false);
        updateStatus('就绪');
    }
}

// 重命名文件
async function renameFiles() {
    const folderPath = prompt('请输入要处理的文件夹路径:');
    if (!folderPath) {
        logMessage('未输入文件夹路径，操作取消', 'warning');
        return;
    }

    showLoading(true);
    updateStatus('重命名中...');

    try {
        const response = await fetch('/api/rename_files', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ folder_path: folderPath })
        });

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                logMessage('文件重命名完成！', 'success');
                logMessage(`成功: ${result.summary.success_count} 个`, 'success');
                logMessage(`跳过: ${result.summary.skip_count} 个`, 'warning');
                logMessage(`错误: ${result.summary.error_count} 个`, 'error');
                logMessage('─'.repeat(50), 'info');

                result.results.forEach(msg => {
                    const type = msg.startsWith('错误:') ? 'error' :
                               msg.startsWith('跳过:') ? 'warning' : 'success';
                    logMessage(msg, type);
                });
            } else {
                logMessage(result.error || '重命名失败', 'error');
            }
        } else {
            throw new Error('重命名请求失败');
        }
    } catch (error) {
        logMessage(`重命名错误: ${error.message}`, 'error');
    } finally {
        showLoading(false);
        updateStatus('就绪');
    }
}

// SKU搜索下载
function searchSkuAndDownload() {
    const sku = document.getElementById('sku-search').value.trim();
    if (!sku) {
        logMessage('请先输入SKU编号', 'warning');
        return;
    }

    // 将SKU添加到商品数据区域并下载
    const productData = document.getElementById('product-data');
    const currentData = productData.value.trim();
    const newData = currentData ? `${currentData}\n${sku}----${sku}` : `${sku}----${sku}`;
    productData.value = newData;

    logMessage(`已添加SKU: ${sku}`, 'info');
    downloadImages();
}

// SKU对比
async function compareSku() {
    const htmlContent = document.getElementById('product-data').value.trim();
    if (!htmlContent) {
        logMessage('请先输入商品HTML数据', 'warning');
        return;
    }

    const folderPath = prompt('请输入要对比的文件夹路径:');
    if (!folderPath) {
        logMessage('未输入文件夹路径，操作取消', 'warning');
        return;
    }

    showLoading(true);
    updateStatus('对比中...');

    try {
        const response = await fetch('/api/compare_sku', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                html_content: htmlContent,
                folder_path: folderPath
            })
        });

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                logMessage('SKU对比完成！', 'success');
                logMessage(`HTML中SKU总数: ${result.total_skus}`, 'info');
                logMessage(`文件夹中文件总数: ${result.total_files}`, 'info');
                logMessage(`匹配数量: ${result.matched_count}`, 'success');
                logMessage(`缺失数量: ${result.missing_count}`, 'error');
                logMessage(`多余数量: ${result.extra_count}`, 'warning');

                if (result.missing_skus.length > 0) {
                    logMessage('─'.repeat(30), 'info');
                    logMessage('缺失的SKU:', 'error');
                    result.missing_skus.forEach(sku => {
                        logMessage(`  - ${sku}`, 'error');
                    });
                }

                if (result.extra_files.length > 0) {
                    logMessage('─'.repeat(30), 'info');
                    logMessage('多余的文件:', 'warning');
                    result.extra_files.forEach(file => {
                        logMessage(`  - ${file}`, 'warning');
                    });
                }

                // 保存缺失的SKU到全局变量，供下载缺失SKU功能使用
                window.missingSkus = result.missing_skus;
            } else {
                logMessage(result.error || '对比失败', 'error');
            }
        } else {
            throw new Error('对比请求失败');
        }
    } catch (error) {
        logMessage(`对比错误: ${error.message}`, 'error');
    } finally {
        showLoading(false);
        updateStatus('就绪');
    }
}

// 下载缺失SKU
async function downloadMissingSku() {
    if (!window.missingSkus || window.missingSkus.length === 0) {
        logMessage('请先执行SKU对比功能以获取缺失的SKU列表', 'warning');
        return;
    }

    showLoading(true);
    updateStatus('下载缺失SKU中...');

    try {
        const response = await fetch('/api/download_missing_sku', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ missing_skus: window.missingSkus })
        });

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                logMessage('缺失SKU下载完成！', 'success');
                logMessage(`下载目录: ${result.download_dir}`, 'info');
                logMessage('─'.repeat(50), 'info');

                result.results.forEach(item => {
                    const statusIcon = item.status === 'success' ? '✓' : '✗';
                    logMessage(`${statusIcon} ${item.message}`, item.status === 'success' ? 'success' : 'error');
                });

                const successCount = result.results.filter(r => r.status === 'success').length;
                const totalCount = result.results.length;
                logMessage(`总计: ${successCount}/${totalCount} 个缺失SKU下载成功`, 'info');
            } else {
                logMessage(result.error || '下载失败', 'error');
            }
        } else {
            throw new Error('下载请求失败');
        }
    } catch (error) {
        logMessage(`下载错误: ${error.message}`, 'error');
    } finally {
        showLoading(false);
        updateStatus('就绪');
    }
}

function browseFolder(inputId) {
    logMessage('文件夹浏览功能需要在桌面环境中使用，请手动输入路径', 'warning');
}

// 工具函数
function showLoading(show) {
    const loading = document.getElementById('loading');
    if (show) {
        loading.classList.remove('hidden');
        isLoading = true;
    } else {
        loading.classList.add('hidden');
        isLoading = false;
    }
}

function updateStatus(message) {
    document.getElementById('status').textContent = message;
}

function logMessage(message, type = 'info') {
    const console = document.getElementById('console');
    const timestamp = new Date().toLocaleTimeString();
    const colors = {
        info: '#e2e8f0',
        success: '#48bb78',
        warning: '#ed8936',
        error: '#f56565'
    };
    
    const logEntry = document.createElement('div');
    logEntry.style.color = colors[type] || colors.info;
    logEntry.style.marginBottom = '5px';
    logEntry.innerHTML = `<span style="color: #a0aec0;">[${timestamp}]</span> ${message}`;
    
    console.appendChild(logEntry);
    console.scrollTop = console.scrollHeight;
}
