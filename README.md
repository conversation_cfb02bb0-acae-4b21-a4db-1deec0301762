# 商品数据提取工具 Web版

这是一个基于Flask的Web应用，用于提取商品信息并下载图片。原本是Tkinter桌面应用，现已转换为现代化的Web界面。

## 功能特性

- ✅ **配置管理**: 支持API地址、Cookie、搜索路径等配置
- ✅ **SKU提取**: 从HTML内容中提取商品SKU编号
- ✅ **商品搜索**: 根据SKU搜索商品名称和信息
- ✅ **图片下载**: 批量下载商品图片
- ✅ **文件重命名**: 批量重命名文件（去除下划线后缀）
- ✅ **SKU对比**: 对比HTML中的SKU与文件夹中的文件
- ✅ **缺失下载**: 下载对比中发现的缺失SKU图片
- ✅ **现代化界面**: 响应式设计，支持移动端访问

## 快速开始

### 方法一：使用启动脚本（推荐）

1. 双击运行 `启动.bat`
2. 脚本会自动：
   - 检查Python环境
   - 创建虚拟环境
   - 安装依赖包
   - 启动Web服务器
3. 在浏览器中访问 `http://localhost:5000`

### 方法二：手动启动

1. 确保已安装Python 3.7+
2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
3. 启动应用：
   ```bash
   python 应用.py
   ```
4. 在浏览器中访问 `http://localhost:5000`

## 使用说明

### 1. 配置设置

在页面顶部的"配置选项"区域设置：

- **API地址**: 商品数据API的URL
- **Cookie**: 访问API所需的认证Cookie
- **搜索路径**: 图片文件的搜索根目录
- **目标路径后缀**: 限制搜索的目标路径（如：`\导出图\已完成`）
- **共享文件夹**: 共享网络文件夹路径

### 2. 商品数据输入

在"商品数据输入"区域：

- 粘贴包含商品信息的HTML内容
- 或使用SKU搜索功能查找单个商品

### 3. 主要功能

#### 提取SKU
- 从输入的HTML内容中提取所有SKU编号
- 显示找到的SKU数量和列表

#### 下载图片
- 根据提取的SKU批量下载商品图片
- 自动创建带时间戳的下载目录
- 显示每个SKU的下载状态

#### 重命名文件
- 批量重命名文件，去除文件名中最后一个下划线及其后的内容
- 例：`产品名称_20240101.jpg` → `产品名称.jpg`

#### SKU对比
- 对比HTML中的SKU与指定文件夹中的文件
- 显示匹配、缺失和多余的文件统计

#### 下载缺失SKU
- 下载SKU对比中发现的缺失商品图片

### 4. 高级选项

- **严格搜索路径限制**: 只在指定目录中搜索文件
- **忽略文件名前后字符**: 启用模糊匹配模式
- **启用目标路径后缀**: 限制搜索特定路径下的文件

## 文件结构

```
半托找图WEB版/
├── 应用.py              # Flask主应用文件
├── templates/
│   └── 主页.html        # 网页模板
├── static/
│   ├── css/
│   │   └── 样式.css     # 样式文件
│   └── js/
│       └── 主脚本.js    # JavaScript交互文件
├── downloads/           # 下载文件存储目录
├── uploads/             # 上传文件存储目录
├── settings.ini         # 配置文件
├── requirements.txt     # Python依赖列表
├── 启动.bat            # Windows启动脚本
└── README.md           # 说明文档
```

## 技术栈

- **后端**: Flask (Python)
- **前端**: HTML5 + CSS3 + JavaScript
- **样式**: 现代化响应式设计
- **图标**: Font Awesome
- **依赖管理**: pip + requirements.txt

## 注意事项

1. **Everything服务**: 需要Everything HTTP服务器运行在本地8080端口
2. **网络访问**: 确保能访问配置的API地址
3. **文件权限**: 确保有读写下载目录的权限
4. **浏览器兼容**: 推荐使用Chrome、Firefox、Edge等现代浏览器

## 故障排除

### 应用无法启动
- 检查Python版本（需要3.7+）
- 确认所有依赖已正确安装
- 查看终端错误信息

### 图片下载失败
- 检查Everything服务是否运行
- 确认搜索路径配置正确
- 检查网络连接和API配置

### 配置不生效
- 刷新页面重新加载配置
- 检查settings.ini文件权限
- 确认配置格式正确

## 更新日志

### v1.0.0 (2024-01-XX)
- 🎉 首次发布Web版本
- ✨ 完整移植桌面版所有功能
- 🎨 现代化响应式界面设计
- 🚀 支持批量操作和实时日志显示

---

如有问题或建议，请联系开发团队。
