from flask import Flask, render_template, request, jsonify, send_file, session
import os
import json
import configparser
from datetime import datetime
import requests
import re
from urllib.parse import quote
import io
from PIL import Image
import threading
import queue
import time

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 请更改为安全的密钥

def validate_key():
    """简化的验证函数 - Web版本"""
    # Web版本暂时跳过验证，或者可以实现基于session的验证
    print("Web版本启动，跳过密钥验证")
    return True

# 调用验证
validate_key()

# 配置文件处理类
class ConfigManager:
    """配置文件管理类"""
    CONFIG_FILE = "settings.ini"
    
    @classmethod
    def load_config(cls):
        """加载配置文件"""
        config = configparser.ConfigParser(interpolation=None)
        
        # 默认配置
        default_settings = {
            'API': {
                'url': 'https://www.dianxiaomi.com/package/list.htm?pageNo=1&pageSize=300&shopId=6833815&state=paid&authId=-1&country=&platform=&isSearch=0&startTime=&endTime=&orderField=order_pay_time&isVoided=0&isRemoved=0&ruleId=-1&sysRule=&applyType=&applyStatus=&printJh=-1&printMd=-1&commitPlatform=&productStatus=&jhComment=-1&storageId=0&history=&custom=-1&isOversea=-1&timeOut=0&refundStatus=0&forbiddenStatus=-1&forbiddenReason=0&behindTrack=-1',
                'cookie': '',
                'base_url': 'https://www.dianxiaomi.com',
                'sku_search_url': 'https://www.dianxiaomi.com/api/popTemuProduct/pageList.json',
                'referer': 'https://www.dianxiaomi.com/'
            },
            'SEARCH': {
                'base_path': r"E:\图片\原图",
                'target_suffix': r"\导出图\已完成",
                'enable_target_suffix': 'True',
                'ignore_filename_chars': 'False',
                'ignore_prefix_count': '20',
                'ignore_suffix_count': '50'
            },
            'EVERYTHING': {
                'base_url': 'http://localhost:8080',
                'image_url': 'http://127.0.0.1:8080',
                'search_path': r"E:\图片\原图",
                'target_suffix': r"\导出图\已完成"
            },
            'SHARED': {
                'folder': r"\\*************\hhr-图库\合伙人-半托出单图\亚克力摆件\丽生-亚克力摆件"
            },
            'OPTIONS': {
                'strict_search': 'True'
            },
            'HEADERS': {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'accept_encoding': 'gzip, deflate, br',
                'accept_language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'connection': 'keep-alive'
            }
        }
        
        # 检查配置文件是否存在
        if os.path.exists(cls.CONFIG_FILE):
            try:
                config.read(cls.CONFIG_FILE, encoding='utf-8')
            except Exception as e:
                print(f"读取配置文件错误: {e}")
        
        # 确保所有默认配置都存在
        for section, options in default_settings.items():
            if not config.has_section(section):
                config.add_section(section)
            for key, value in options.items():
                if not config.has_option(section, key):
                    config[section][key] = value
        
        # 保存更新后的配置
        cls.save_config(config)
        return config
    
    @classmethod
    def save_config(cls, config):
        """保存配置到文件"""
        try:
            with open(cls.CONFIG_FILE, 'w', encoding='utf-8') as f:
                config.write(f)
            return True
        except Exception as e:
            print(f"保存配置文件错误: {e}")
            return False
            
    @classmethod
    def update_config(cls, section, key, value):
        """更新单个配置项"""
        config = cls.load_config()
        if not config.has_section(section):
            config.add_section(section)
        config[section][key] = value
        return cls.save_config(config)

# 全局配置
config = ConfigManager.load_config()

@app.route('/')
def index():
    """主页"""
    return render_template('主页.html', config=config)

@app.route('/api/config', methods=['GET', 'POST'])
def handle_config():
    """处理配置相关请求"""
    if request.method == 'GET':
        # 返回当前配置
        config = ConfigManager.load_config()
        config_dict = {}
        for section in config.sections():
            config_dict[section] = dict(config[section])
        return jsonify(config_dict)
    
    elif request.method == 'POST':
        # 更新配置
        data = request.get_json()
        section = data.get('section')
        key = data.get('key')
        value = data.get('value')
        
        if section and key and value is not None:
            success = ConfigManager.update_config(section, key, value)
            return jsonify({'success': success})
        
        return jsonify({'success': False, 'error': '参数不完整'})

@app.route('/api/extract_sku', methods=['POST'])
def extract_sku():
    """提取SKU"""
    try:
        data = request.get_json()
        html_content = data.get('html_content', '')
        
        if not html_content:
            return jsonify({'success': False, 'error': '请输入HTML内容'})
        
        # 提取SKU
        skus = SkuExtractor.extract_skus(html_content)
        product_name = SkuExtractor.extract_product_name(html_content)
        
        return jsonify({
            'success': True,
            'skus': skus,
            'product_name': product_name,
            'count': len(skus)
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# SKU提取功能模块
class SkuExtractor:
    """SKU提取功能模块"""

    @staticmethod
    def extract_skus(html_content):
        """从HTML内容中提取SKU编号"""
        pattern = r'<a class="pairProInfoSku productUrl"[^>]*>([A-Za-z0-9]+)</a>'
        matches = re.findall(pattern, html_content)
        return list(set(matches))

    @staticmethod
    def extract_product_name(html_content):
        """从HTML内容中提取商品名称"""
        pattern = r'<span class="white-space no-new-line3 productHead">(.*?)</span>'
        match = re.search(pattern, html_content)
        if match:
            return match.group(1).strip()
        return None

# 图片下载功能模块
class ImageDownloader:
    """图片下载功能模块"""
    target_path_suffix = r"\导出图\已完成"
    enable_target_suffix = True
    ignore_filename_chars = False
    ignore_prefix_count = 20
    ignore_suffix_count = 50

    @staticmethod
    def format_size(bytes_size):
        """格式化文件大小"""
        if bytes_size == 0:
            return "0 MB"
        mb_size = bytes_size / (1024 * 1024)
        if mb_size < 1024:
            return f"{mb_size:.2f} MB"
        return f"{mb_size / 1024:.2f} GB"

    @classmethod
    def should_download_file(cls, file_path, file_name):
        """检查文件是否符合下载条件"""
        if not os.path.splitext(file_name)[1].lower() in ('.png', '.jpg', '.jpeg'):
            return False

        if not cls.enable_target_suffix:
            return True

        if cls.target_path_suffix:
            lower_file_path = file_path.lower()
            lower_suffix = cls.target_path_suffix.lower()

            if not lower_file_path.endswith(lower_suffix + '\\' + file_name.lower()) and \
               not lower_file_path.endswith(lower_suffix + '/' + file_name.lower()):
                return False

        return True

    @classmethod
    def compare_filenames(cls, filename, search_term):
        """比较文件名和搜索词，支持忽略前后字符"""
        if not cls.ignore_filename_chars:
            return search_term.lower() in filename.lower()

        # 获取文件名（不含扩展名）
        name_without_ext = os.path.splitext(filename)[0]

        # 如果文件名长度小于忽略的字符数，直接进行模糊匹配
        if len(name_without_ext) <= cls.ignore_prefix_count + cls.ignore_suffix_count:
            half_term_length = max(3, len(search_term) // 2)
            search_part = search_term[:half_term_length].lower()
            return search_part in name_without_ext.lower()

        # 截取中间部分
        middle_part = name_without_ext[cls.ignore_prefix_count:-cls.ignore_suffix_count if cls.ignore_suffix_count > 0 else None]

        # 模糊匹配
        search_term_lower = search_term.lower()
        middle_part_lower = middle_part.lower()

        # 检查中间部分是否包含搜索词
        if search_term_lower in middle_part_lower:
            return True

        # 检查搜索词是否包含中间部分
        if middle_part_lower in search_term_lower:
            return True

        # 检查是否有足够的相似性（至少有3个连续字符匹配）
        min_match_length = min(3, min(len(search_term_lower), len(middle_part_lower)))

        # 检查搜索词中是否有任何连续的部分匹配中间部分
        for i in range(len(search_term_lower) - min_match_length + 1):
            search_part = search_term_lower[i:i+min_match_length]
            if search_part in middle_part_lower:
                return True

        # 检查中间部分是否有任何连续的部分匹配搜索词
        for i in range(len(middle_part_lower) - min_match_length + 1):
            middle_part_section = middle_part_lower[i:i+min_match_length]
            if middle_part_section in search_term_lower:
                return True

        return False

    @classmethod
    def process_search_term(cls, search_term):
        """处理搜索词，支持忽略前后字符"""
        if not cls.ignore_filename_chars:
            return search_term

        if len(search_term) <= cls.ignore_prefix_count + cls.ignore_suffix_count:
            return search_term

        middle_part = search_term[cls.ignore_prefix_count:-cls.ignore_suffix_count if cls.ignore_suffix_count > 0 else None]
        return middle_part.strip()

@app.route('/api/search_sku', methods=['POST'])
def search_sku():
    """搜索SKU商品名称"""
    try:
        data = request.get_json()
        sku = data.get('sku', '').strip()

        if not sku:
            return jsonify({'success': False, 'error': '请输入SKU'})

        config = ConfigManager.load_config()

        # 构建搜索请求
        search_url = config['API']['sku_search_url']
        headers = {
            'User-Agent': config['HEADERS']['user_agent'],
            'Cookie': config['API']['cookie'],
            'Referer': config['API']['referer'],
            'Content-Type': 'application/json'
        }

        search_data = {
            'pageNo': 1,
            'pageSize': 20,
            'sku': sku
        }

        response = requests.post(search_url, json=search_data, headers=headers, timeout=30)
        response.raise_for_status()

        result = response.json()

        if result.get('success') and result.get('data'):
            products = result['data'].get('list', [])
            if products:
                product = products[0]
                return jsonify({
                    'success': True,
                    'product_name': product.get('productName', ''),
                    'sku': product.get('sku', ''),
                    'image_url': product.get('imageUrl', ''),
                    'price': product.get('price', '')
                })

        return jsonify({'success': False, 'error': '未找到相关商品'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/download_images', methods=['POST'])
def download_images():
    """下载商品图片"""
    try:
        data = request.get_json()
        html_content = data.get('html_content', '')

        if not html_content:
            return jsonify({'success': False, 'error': '请提供HTML内容'})

        # 提取SKU和商品名称
        skus = SkuExtractor.extract_skus(html_content)
        product_name = SkuExtractor.extract_product_name(html_content)

        if not skus:
            return jsonify({'success': False, 'error': '未找到有效的SKU'})

        config = ConfigManager.load_config()

        # 创建下载目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        download_dir = os.path.join('downloads', f'商品图片_{timestamp}')
        os.makedirs(download_dir, exist_ok=True)

        results = []
        for sku in skus:
            try:
                # 搜索图片
                search_result = search_images_for_sku(sku, config)
                if search_result['success']:
                    # 下载图片
                    download_result = download_image_for_sku(sku, search_result['images'], download_dir, config)
                    results.append({
                        'sku': sku,
                        'status': 'success' if download_result['success'] else 'error',
                        'message': download_result['message'],
                        'image_count': download_result.get('count', 0)
                    })
                else:
                    results.append({
                        'sku': sku,
                        'status': 'error',
                        'message': search_result['error'],
                        'image_count': 0
                    })
            except Exception as e:
                results.append({
                    'sku': sku,
                    'status': 'error',
                    'message': str(e),
                    'image_count': 0
                })

        return jsonify({
            'success': True,
            'results': results,
            'download_dir': download_dir,
            'product_name': product_name
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def search_images_for_sku(sku, config):
    """为SKU搜索图片"""
    try:
        # 构建搜索查询
        search_path = config['SEARCH']['base_path']
        strict_search = config['OPTIONS'].getboolean('strict_search', True)

        search_query = sku
        if search_path and strict_search:
            if not search_path.endswith('\\'):
                search_path += '\\'
            search_query = f'path:"{search_path}" {sku}'

        # Everything搜索参数
        search_params = {
            "search": search_query,
            "json": 1,
            "path_column": 1,
            "size_column": 1,
            "sort": "name",
            "ascending": 1
        }

        everything_url = config['EVERYTHING']['base_url']
        response = requests.get(everything_url, params=search_params, timeout=30)
        response.raise_for_status()

        data = response.json()
        results = data.get('results', [])

        # 过滤有效的图片文件
        valid_images = []
        for item in results:
            file_name = item.get('name', '')
            file_path = f"{item.get('path', '')}\\{file_name}".replace("\\\\", "\\")

            if ImageDownloader.should_download_file(file_path, file_name):
                if ImageDownloader.compare_filenames(file_name, sku):
                    valid_images.append({
                        'name': file_name,
                        'path': file_path,
                        'size': item.get('size', 0)
                    })

        return {
            'success': True,
            'images': valid_images,
            'total_found': len(results)
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def download_image_for_sku(sku, images, download_dir, config):
    """为SKU下载图片"""
    try:
        if not images:
            return {
                'success': False,
                'message': f'SKU {sku} 未找到匹配的图片'
            }

        # 如果只有一张图片，直接下载
        # 如果有多张图片，选择第一张（在实际应用中可能需要用户选择）
        selected_image = images[0]

        # 构建下载URL
        image_url = f"{config['EVERYTHING']['image_url']}/{quote(selected_image['path'])}"

        # 确定文件扩展名
        original_ext = os.path.splitext(selected_image['name'])[1].lower()
        new_filename = f"{sku}{original_ext}"
        save_path = os.path.join(download_dir, new_filename)

        # 下载图片
        headers = {
            'User-Agent': config['HEADERS']['user_agent'],
            'Accept': config['HEADERS']['accept'],
            'Accept-Encoding': config['HEADERS']['accept_encoding'],
            'Accept-Language': config['HEADERS']['accept_language'],
            'Connection': config['HEADERS']['connection']
        }

        response = requests.get(image_url, headers=headers, stream=True, timeout=15)
        response.raise_for_status()

        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(1024):
                f.write(chunk)

        return {
            'success': True,
            'message': f'SKU {sku} 下载成功 → {new_filename}',
            'count': 1,
            'filename': new_filename
        }

    except Exception as e:
        return {
            'success': False,
            'message': f'SKU {sku} 下载失败: {str(e)}'
        }

@app.route('/api/rename_files', methods=['POST'])
def rename_files():
    """重命名文件功能"""
    try:
        data = request.get_json()
        folder_path = data.get('folder_path', '')

        if not folder_path or not os.path.exists(folder_path):
            return jsonify({'success': False, 'error': '请提供有效的文件夹路径'})

        success_count = 0
        skip_count = 0
        error_count = 0
        results = []

        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)

            if not os.path.isfile(file_path):
                continue

            name_part, ext = os.path.splitext(filename)
            last_underscore = name_part.rfind('_')

            if last_underscore != -1:
                new_name = name_part[:last_underscore] + ext
                new_path = os.path.join(folder_path, new_name)

                if os.path.exists(new_path):
                    skip_count += 1
                    results.append(f'跳过: {filename} (目标文件已存在)')
                else:
                    try:
                        os.rename(file_path, new_path)
                        success_count += 1
                        results.append(f'重命名: {filename} → {new_name}')
                    except Exception as e:
                        error_count += 1
                        results.append(f'错误: {filename} - {str(e)}')
            else:
                skip_count += 1
                results.append(f'跳过: {filename} (无下划线)')

        return jsonify({
            'success': True,
            'summary': {
                'success_count': success_count,
                'skip_count': skip_count,
                'error_count': error_count
            },
            'results': results
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/compare_sku', methods=['POST'])
def compare_sku():
    """SKU对比功能"""
    try:
        data = request.get_json()
        html_content = data.get('html_content', '')
        folder_path = data.get('folder_path', '')

        if not html_content:
            return jsonify({'success': False, 'error': '请提供HTML内容'})

        if not folder_path or not os.path.exists(folder_path):
            return jsonify({'success': False, 'error': '请提供有效的文件夹路径'})

        # 提取SKU
        skus_from_html = set(SkuExtractor.extract_skus(html_content))

        # 获取文件夹中的文件名（去除扩展名）
        files_in_folder = set()
        for filename in os.listdir(folder_path):
            if os.path.isfile(os.path.join(folder_path, filename)):
                name_without_ext = os.path.splitext(filename)[0]
                files_in_folder.add(name_without_ext)

        # 对比结果
        missing_skus = skus_from_html - files_in_folder
        extra_files = files_in_folder - skus_from_html
        matched_skus = skus_from_html & files_in_folder

        return jsonify({
            'success': True,
            'total_skus': len(skus_from_html),
            'total_files': len(files_in_folder),
            'matched_count': len(matched_skus),
            'missing_count': len(missing_skus),
            'extra_count': len(extra_files),
            'missing_skus': list(missing_skus),
            'extra_files': list(extra_files),
            'matched_skus': list(matched_skus)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/download_missing_sku', methods=['POST'])
def download_missing_sku():
    """下载缺失的SKU图片"""
    try:
        data = request.get_json()
        missing_skus = data.get('missing_skus', [])

        if not missing_skus:
            return jsonify({'success': False, 'error': '没有缺失的SKU需要下载'})

        config = ConfigManager.load_config()

        # 创建下载目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        download_dir = os.path.join('downloads', f'缺失SKU_{timestamp}')
        os.makedirs(download_dir, exist_ok=True)

        results = []
        for sku in missing_skus:
            try:
                search_result = search_images_for_sku(sku, config)
                if search_result['success']:
                    download_result = download_image_for_sku(sku, search_result['images'], download_dir, config)
                    results.append({
                        'sku': sku,
                        'status': 'success' if download_result['success'] else 'error',
                        'message': download_result['message']
                    })
                else:
                    results.append({
                        'sku': sku,
                        'status': 'error',
                        'message': search_result['error']
                    })
            except Exception as e:
                results.append({
                    'sku': sku,
                    'status': 'error',
                    'message': str(e)
                })

        return jsonify({
            'success': True,
            'results': results,
            'download_dir': download_dir
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    # 创建必要的文件夹
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    os.makedirs('static/images', exist_ok=True)
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('downloads', exist_ok=True)

    print("🚀 启动商品数据提取工具Web版...")
    print("📍 访问地址: http://127.0.0.1:5001")
    print("🛑 按Ctrl+C停止服务器")
    print("-" * 50)

    try:
        app.run(debug=False, host='127.0.0.1', port=5001, threaded=True)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print(f"错误详情: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
