@echo off
chcp 65001
echo 启动商品数据提取工具Web版...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

REM 检查是否存在虚拟环境
if not exist "venv" (
    echo 创建虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat

REM 安装依赖
echo 安装依赖包...
pip install -r requirements.txt

REM 启动应用
echo.
echo 启动Web应用...
echo 请在浏览器中访问: http://localhost:5000
echo 按Ctrl+C停止服务器
echo.
python 应用.py

pause
