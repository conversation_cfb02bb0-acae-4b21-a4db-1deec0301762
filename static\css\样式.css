/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  color: #2d3748;
  line-height: 1.6;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-top: 20px;
  margin-bottom: 20px;
}

/* 头部样式 */
.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;
  border-bottom: 2px solid #e2e8f0;
}

.header h1 {
  font-size: 2.5rem;
  color: #4299e1;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header h1 i {
  margin-right: 15px;
  color: #48bb78;
}

.subtitle {
  font-size: 1.1rem;
  color: #4a5568;
  font-weight: 300;
}

/* 区域标题样式 */
section h2 {
  font-size: 1.4rem;
  color: #2d3748;
  margin-bottom: 20px;
  padding: 10px 0;
  border-bottom: 2px solid #4299e1;
  display: flex;
  align-items: center;
}

section h2 i {
  margin-right: 10px;
  color: #4299e1;
}

/* 配置区域样式 */
.config-section {
  margin-bottom: 30px;
  padding: 25px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.config-group {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.config-group label {
  min-width: 120px;
  font-weight: 500;
  color: #2d3748;
  text-align: right;
}

.config-input {
  flex: 1;
  min-width: 300px;
  padding: 12px 15px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #ffffff;
}

.config-input:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  transform: translateY(-1px);
}

.input-group {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.number-input {
  width: 60px;
  padding: 8px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  text-align: center;
  font-size: 14px;
}

.number-input:focus {
  outline: none;
  border-color: #4299e1;
}

/* 复选框样式 */
.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: #2d3748;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  background: #ffffff;
  border: 2px solid #e2e8f0;
  border-radius: 4px;
  margin-right: 8px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: #4299e1;
  border-color: #4299e1;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 12px;
}

.ignore-chars-config {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 20px;
  font-size: 14px;
  color: #4a5568;
}

.help-text {
  font-size: 13px;
  color: #ed8936;
  font-style: italic;
  margin-left: 20px;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 商品数据输入区域 */
.product-section {
  margin-bottom: 30px;
  padding: 25px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.product-textarea {
  width: 100%;
  min-height: 120px;
  padding: 15px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-family: "Consolas", "Monaco", monospace;
  font-size: 14px;
  resize: vertical;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.product-textarea:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  background: #ffffff;
}

.sku-search-group {
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.sku-search-group label {
  min-width: 80px;
  font-weight: 500;
  color: #2d3748;
}

/* 按钮样式 */
.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn:active {
  transform: translateY(0);
}

.btn-secondary {
  background: #a0aec0;
  color: white;
}

.btn-secondary:hover {
  background: #718096;
}

.btn-orange {
  background: #ed8936;
  color: white;
}

.btn-orange:hover {
  background: #dd6b20;
}

.btn-blue {
  background: #4299e1;
  color: white;
}

.btn-blue:hover {
  background: #3182ce;
}

.btn-green {
  background: #48bb78;
  color: white;
}

.btn-green:hover {
  background: #38a169;
}

.btn-purple {
  background: #9f7aea;
  color: white;
}

.btn-purple:hover {
  background: #805ad5;
}

.btn-red {
  background: #f56565;
  color: white;
}

.btn-red:hover {
  background: #e53e3e;
}

.btn-success {
  background: #48bb78;
  color: white;
}

.btn-success:hover {
  background: #38a169;
}

/* 操作按钮区域 */
.actions-section {
  margin-bottom: 30px;
  padding: 25px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
}

/* 控制台样式 */
.console-section {
  margin-bottom: 30px;
  padding: 25px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.console {
  background: #1a202c;
  color: #e2e8f0;
  padding: 20px;
  border-radius: 8px;
  font-family: "Consolas", "Monaco", monospace;
  font-size: 13px;
  line-height: 1.5;
  min-height: 300px;
  max-height: 500px;
  overflow-y: auto;
  border: 2px solid #2d3748;
}

.console::-webkit-scrollbar {
  width: 8px;
}

.console::-webkit-scrollbar-track {
  background: #2d3748;
  border-radius: 4px;
}

.console::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 4px;
}

.console::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* 状态栏 */
.status-bar {
  padding: 12px 20px;
  background: #f7fafc;
  border-top: 1px solid #e2e8f0;
  border-radius: 0 0 12px 12px;
  font-size: 14px;
  color: #4a5568;
  text-align: left;
}

/* 加载提示 */
.loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading.hidden {
  display: none;
}

.loading-spinner {
  background: white;
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.loading-spinner i {
  font-size: 2rem;
  color: #4299e1;
  margin-bottom: 15px;
}

.loading-spinner span {
  display: block;
  font-size: 16px;
  color: #2d3748;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    margin: 10px;
    padding: 15px;
  }
  
  .config-group {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .config-group label {
    min-width: auto;
    text-align: left;
    margin-bottom: 5px;
  }
  
  .config-input {
    min-width: 100%;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  .header h1 {
    font-size: 2rem;
  }
}
